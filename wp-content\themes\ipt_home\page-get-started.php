<?php
/**
 * Template Name: Get Started page template
 *
 * @package ipt_home
 */

// Check if user is logged in
if (!is_user_logged_in()) {
    wp_redirect(home_url('login'));
    exit;
}

// Get current user and customer_id
$current_user_id = get_current_user_id();
$customer_id = get_user_meta($current_user_id, 'customer_id', true);

// If no customer_id found, redirect to login to re-authenticate
if (empty($customer_id)) {
    error_log("Get Started: No customer_id found for user {$current_user_id}. Redirecting to login.");
    wp_redirect(home_url('login'));
    exit;
}

get_header(null, array(
    'hide_menu' => true,
));

$step = $_GET['step'] ?? 1;
$total_steps = 6;
?>
<main class="min-h-screen w-full">
    <div class="bg-white overflow-hidden">
      <div class="gap-5 flex max-md:flex-col max-md:items-stretch">
        <!-- Left Content -->
        <div class="w-7/12 max-md:w-full min-h-full">
          <div class="flex w-full flex-col max-md:max-w-full">
            <!-- Logo -->
            <div class="flex items-center pl-[16px] md:pl-[80px] gap-1 overflow-hidden text-base text-brand-main font-bold whitespace-nowrap tracking-tight py-[7px]">
              <img
                src="https://cdn.builder.io/api/v1/image/assets/ffcb3c9a2de94502ae520638565230e0/5e2c751f3df0530176bcf2a042269af9fcc94c33?placeholderIfAbsent=true"
                class="aspect-[1] object-contain w-8 self-stretch shrink-0 my-auto"
                alt="Weaveform Logo"
              />
              <div class="bg-blend-normal self-stretch my-auto">Weaveform</div>
            </div>
            
            <div class="flex max-w-full  flex-col items-stretch mt-[29px] px-[16px] md:pl-[188px] md:pr-[128px]">
              <!-- Progress Indicator -->
                <div class="flex items-center gap-3 min-h-[40px] mb-4">
                    <?php for($i = 2; $i <= $total_steps; $i++) : ?>
                    <div class="self-stretch flex w-[60px] shrink-0 h-[5px] my-auto rounded-[80px] <?php 
            echo ($i <= $step) ? 'bg-brand-main' : 'bg-disable-step'; 
        ?>" aria-label="Step <?php echo $i; ?>"></div>
                    <?php endfor; ?>
                    <!-- <div class="self-stretch flex w-[60px] shrink-0 h-[5px] my-auto rounded-[80px] bg-disable-step" aria-label="Step 2"></div>
                    <div class="self-stretch flex w-[60px] shrink-0 h-[5px] my-auto rounded-[80px] bg-disable-step" aria-label="Step 3"></div>
                    <div class="self-stretch flex w-[60px] shrink-0 h-[5px] my-auto rounded-[80px] bg-disable-step" aria-label="Step 4"></div> -->
                </div>
                <!-- Content Step -->
                <div class="flex flex-col justify-start min-h-[300px] h-auto text-base gap-[16px] font-semibold tracking-tight max-md:max-w-full">
                   
                    <!-- Step 1 -->
                    <?php if ($step == 1) : ?>
                        <h1 class="text-primary-main text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                            Welcome to Weaveform!
                        </h1>
                        <p class="text-black font-medium leading-6 mt-4 max-md:max-w-full">
                            We'll guide you through a few simple steps to set up your fully
                            managed website – no technical know-how required.
                        </p>
                        <div class="mt-4 w-[196px] max-w-full">
                            <!-- Get started button -->
                            <button 
                                onclick="goToStep(2)"
                                class="mx-auto rounded-[8px] text-primary-main bg-brand-main hover:bg-brand-main/80 text-16 font-semibold self-stretch cursor-pointer  py-[12px] px-[20px] border-[none] w-[196px]">
                                Get Started
                            </button>
                        </div>
                    <?php endif; ?>
                     <!-- Step 2 New -->
                    <?php if ($step == 2) : ?>
                        <h1 class="text-[#21272A] text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                            What is your industry?
                        </h1>
                        
                        <p class="text-gray-600 text-lg mb-8">
                            Select the industry that best represent your brand.
                        </p>

                        <form class="flex flex-col gap-8">
                            <!-- Industry Dropdown -->
                            <div class="flex flex-col gap-2">
                                <label class="text-[#21272A] text-xl font-medium">
                                    Industry Options
                                </label>
                                <!-- Industry Dropdown Container -->
                                <div class="relative z-30">
                                    <select id="industry"
                                            class="w-full !px-[16px] !py-[12px] rounded-lg border border-[#E6E9F5]
                                                appearance-none bg-white
                                                focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                                cursor-pointer
                                                transition-all duration-200 !h-[48px] text-16 !bg-ipt-bg-1 select-custom">
                                        <option value="" disabled>Select Industry</option>
                                    </select>

                                    <!-- Custom Dropdown Content -->
                                    <div id="industryDropdown"
                                        class="dropdown-content absolute left-0 right-0 top-full mt-1 hidden
                                                !bg-white rounded-lg border border-[#E6E9F5] shadow-lg
                                                max-h-[300px] overflow-y-auto
                                                overflow-hidden z-100">
                                        <!-- Dropdown Items -->
                                        <div class="py-2 bg-white">

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--  Company Description -->
                            <div class="flex flex-col gap-2">
                                <label class="text-[#21272A] text-xl font-medium">
                                    Company Description
                                </label>
                                <!--  Company Description -->
                                <div class="relative">
                                    <textarea 
                                    id="company_description" 
                                    name="company_description" 
                                    placeholder="Please enter your company description"
                                    rows="4"
                                    class="w-full !px-[16px] !py-[12px] rounded-lg !bg-ipt-bg-1
                                            focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                            placeholder:text-gray-400
                                            transition-all duration-200 resize-vertical"></textarea>
                                </div>
                            </div>
                            <script>
                                jQuery(document).ready(function($) {
                                    /* Industry list */
                                    const queryIndustry = `
                                        query Industry_list {
                                            industry_list(body: { filters: null }) {
                                                id
                                                name
                                            }
                                        }
                                    `;

                                    $.ajax({
                                        url: iptHomeAjax.ajax_url,
                                        type: 'POST',
                                        dataType: 'json',
                                        data: {
                                            action: 'ipt_home_graphql',
                                            query: queryIndustry,
                                            variables: {}
                                        },
                                        success: function(response) {
                                            if(response.data && response.data.industry_list) {      
                                                renderIndustries(response.data.industry_list);
                                            } else {
                                                // No data
                                            }
                                        },
                                        error: function(xhr, status, error) {
                                        }
                                    });

                                    // Hàm render ra HTML
                                    function renderIndustries(list) {
                                        let html = '<option value="" disabled>Select Industry</option>';
                                        list.forEach(function(item) {
                                            html += `
                                                 <option value="${item.id}">${item.name}</option>
                                            `;
                                        });
                                        $('#industry').html(html);

                                        let html_dropdown = '';  
                                        list.forEach(function(item) {
                                            html_dropdown += `
                                                <div data-industry-id="${item.id}" class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                                                    ${item.name}
                                                </div>
                                            `;      
                                        });
                                        $('#industryDropdown').html(html_dropdown);
                                    }   
                                    
                                });
                            </script> 

                            <!-- Navigation Buttons -->
                            <div class="flex gap-4 mt-8">
                               <!-- Back Button -->
                               <button type="button" 
                                        onclick="goToStep(2)"
                                        class="flex-1 py-3 px-6 rounded-lg border border-btn-disable
                                            text-primary-main font-semibold hover:border-btn-disable
                                            hover:bg-gray-50 transition-all duration-200">
                                    Back
                                </button>

                                <!-- Continue Button -->
                                <button onclick="goToStep(3)" type="button" 
                                        id="continueBtn"
                                        class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                             transition-all duration-200"
                                        >
                                    Continue
                                </button>
                            </div>
                        </form>
                        <script>
                           document.addEventListener('DOMContentLoaded', function() {
                                // Function to initialize dropdown
                                function initializeDropdown(selectId, dropdownId) {
                                    const select = document.getElementById(selectId);
                                    const dropdown = document.getElementById(dropdownId);

                                    // Check if elements exist before proceeding
                                    if (!select || !dropdown) {
                                        console.log('Dropdown elements not found:', selectId, dropdownId);
                                        return;
                                    }

                                    const dropdownItems = dropdown.querySelectorAll('.cursor-pointer');
                                    const selectContainer = select.parentElement;

                                    // Check if selectContainer exists
                                    if (!selectContainer) {
                                        console.log('Select container not found for:', selectId);
                                        return;
                                    }

                                    // Click on container to toggle dropdown
                                    selectContainer.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        
                                        // Close all other dropdowns
                                        document.querySelectorAll('.dropdown-content').forEach(d => {
                                            if (d.id !== dropdownId) {
                                                d.classList.add('hidden');
                                            }
                                        });
                                        
                                        dropdown.classList.toggle('hidden');
                                    });

                                    // Handle item selection
                                    dropdownItems.forEach(item => {
                                        item.addEventListener('click', function(e) {
                                            e.stopPropagation();
                                            const selectedValue = this.textContent.trim();
                                            const selectedId = this.getAttribute('data-industry-id');

                                            // Clear all options and add the selected one
                                            select.innerHTML = '';

                                            // Add default option
                                            const defaultOption = document.createElement('option');
                                            defaultOption.value = '';
                                            defaultOption.text = 'Select Industry';
                                            defaultOption.disabled = true;
                                            select.appendChild(defaultOption);

                                            // Add selected option
                                            const selectedOption = document.createElement('option');
                                            selectedOption.value = selectedId;
                                            selectedOption.text = selectedValue;
                                            selectedOption.selected = true;
                                            select.appendChild(selectedOption);

                                            // Hide dropdown
                                            dropdown.classList.add('hidden');

                                            // Trigger change event for data persistence
                                            select.dispatchEvent(new Event('change'));

                                            // Update selected state in dropdown
                                            dropdownItems.forEach(di => di.classList.remove('bg-ipt-bg-1'));
                                            this.classList.add('bg-ipt-bg-1');
                                        });
                                    });

                                    // Close dropdown when clicking outside
                                    document.addEventListener('click', function(e) {
                                        if (!selectContainer.contains(e.target)) {
                                            dropdown.classList.add('hidden');
                                        }
                                    });
                                }

                                // Initialize all necessary dropdowns for Step 2
                                const dropdownConfigs = [
                                    { selectId: 'industry', dropdownId: 'industryDropdown' }
                                ];

                                // Initialize all dropdowns
                                dropdownConfigs.forEach(config => {
                                    initializeDropdown(config.selectId, config.dropdownId);
                                });

                                // Add validation for form (optional for Step 2)
                                function validateDropdowns() {
                                    // No validation needed for Step 2 since fields are optional
                                    const continueBtn = document.getElementById('continueBtn');
                                    if (continueBtn) {
                                        continueBtn.disabled = false;
                                        continueBtn.classList.remove('bg-disable-step', 'cursor-not-allowed', 'text-white');
                                        continueBtn.classList.add('bg-brand-main', 'cursor-pointer', 'text-primary-main');
                                    }
                                }

                                // Add change listeners for validation
                                dropdownConfigs.forEach(config => {
                                    const element = document.getElementById(config.selectId);
                                    if (element) {
                                        element.addEventListener('change', validateDropdowns);
                                    }
                                });

                                // Initialize validation state
                                validateDropdowns();
                            });
                        </script>
                        <style>
                            .select-custom {
                                -webkit-appearance: none !important;
                                -moz-appearance: none !important;
                                appearance: none !important;
                                pointer-events: none;
                            }
                            .select-custom::-ms-expand {
                                display: none;
                            }
                            .select-custom option {
                                display: none;
                            }
                            
                            /* Custom scrollbar for dropdown */
                            .dropdown-content {
                                background-color: #fff;
                                z-index: 9999 !important;
                            }
                            .dropdown-content::-webkit-scrollbar {
                                width: 8px;
                            }

                            .dropdown-content::-webkit-scrollbar-track {
                                background: #f1f1f1;
                                border-radius: 4px;
                            }

                            .dropdown-content::-webkit-scrollbar-thumb {
                                background: #888;
                                border-radius: 4px;
                            }

                            .dropdown-content::-webkit-scrollbar-thumb:hover {
                                background: #555;
                            }

                            /* Ensure dropdown is above other elements */
                            .relative {
                                position: relative;
                            }
                        </style>
                    <?php endif; ?>

                    <!-- Step 3 -->
                    <?php if ($step == 3) : ?>
                        <h1 class="text-primary-main text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                           Which services or products do you offer?
                        </h1>

                        <form class="flex flex-col gap-8 mt-8">
                            <!-- Business Name Input -->
                            <div class="flex flex-col gap-2">
                                <label for="services_products" class="text-primary-main text-base font-medium">
                                    Please enter your services or products
                                </label>
                                
                                <!--  services_products -->
                                <div class="relative">
                                    <textarea 
                                    id="services_products" 
                                    name="services_products" 
                                    placeholder="Please enter your company description"
                                    rows="4"
                                    class="w-full !px-[16px] !py-[12px] rounded-lg !bg-ipt-bg-1
                                            focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                            placeholder:text-gray-400
                                            transition-all duration-200 resize-vertical"></textarea>
                                </div>
                            </div>

                         

                            <!-- Navigation Buttons -->
                            <div class="flex gap-4 mt-8">
                                <!-- Back Button -->
                                <button type="button" 
                                        onclick="goToStep(2)"
                                        class="flex-1 py-3 px-6 rounded-lg border border-btn-disable
                                            text-primary-main font-semibold hover:border-btn-disable
                                            hover:bg-gray-50 transition-all duration-200">
                                    Back
                                </button>

                                <!-- Continue Button -->
                                <button type="button" 
                                        id="continueBtn"
                                        onclick="goToStep(4)"
                                        class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                             transition-all duration-200"
                                        >
                                    Continue
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>

                    <!-- Step 4 -->
                    <?php if ($step == 4) : ?>
                        <h1 class="text-primary-main text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                          What are your customer demographics (e.g., age, gender, interests, ethnicity, occupation)?
                        </h1>

                        <form class="flex flex-col gap-8 mt-8">
                            <!-- Business Name Input -->
                            <div class="flex flex-col gap-2">
                                <label for="customer_demographics" class="text-primary-main text-base font-medium">
                                    Please enter your customer demographics
                                </label>
                                 <!--  customer_demographics -->
                                <div class="relative">
                                    <textarea 
                                    id="customer_demographics" 
                                    name="customer_demographics" 
                                    placeholder="Please enter your customer demographics"
                                    rows="4"
                                    class="w-full !px-[16px] !py-[12px] rounded-lg !bg-ipt-bg-1
                                            focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                            placeholder:text-gray-400
                                            transition-all duration-200 resize-vertical"></textarea>
                                </div>
                            </div>

                         

                            <!-- Navigation Buttons -->
                            <div class="flex gap-4 mt-8">
                                <!-- Back Button -->
                                <button type="button" 
                                        onclick="goToStep(2)"
                                        class="flex-1 py-3 px-6 rounded-lg border border-btn-disable
                                            text-primary-main font-semibold hover:border-btn-disable
                                            hover:bg-gray-50 transition-all duration-200">
                                    Back
                                </button>

                                <!-- Continue Button -->
                                <button type="button" 
                                        id="continueBtn"
                                        onclick="goToStep(5)"
                                        class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                             transition-all duration-200"
                                        >
                                    Continue
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>

                    <!-- Step 5 New -->
                    <?php if ($step == 5) : ?>
                        <h1 class="text-[#21272A] text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                            Where are your products used?
                        </h1>
                       
                        <form class="flex flex-col gap-8">
                            <!-- Business Name Input -->
                            <div class="flex flex-col gap-2">
                                <label for="products_used" class="text-primary-main text-base font-medium">
                                   Where are your products used?
                                </label>
                                 <!--  products_used -->
                                <div class="relative">
                                    <textarea 
                                    id="products_used" 
                                    name="products_used" 
                                    placeholder="Please enter where are your products used"
                                    rows="4"
                                    class="w-full !px-[16px] !py-[12px] rounded-lg !bg-ipt-bg-1
                                            focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                            placeholder:text-gray-400
                                            transition-all duration-200 resize-vertical"></textarea>
                                </div>
                            </div>
                        
                            <!-- Navigation Buttons -->
                            <div class="flex gap-4 mt-8">
                               <!-- Back Button -->
                               <button type="button" 
                                        onclick="goToStep(3)"
                                        class="flex-1 py-3 px-6 rounded-lg border border-btn-disable
                                            text-primary-main font-semibold hover:border-btn-disable
                                            hover:bg-gray-50 transition-all duration-200">
                                    Back
                                </button>

                                <!-- Continue Button -->
                                <button onclick="goToStep(6)" type="button" 
                                        id="continueBtn"
                                        class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                             transition-all duration-200"
                                        >
                                    Continue
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>

                    <!-- Step 6 New -->
                    <?php if ($step == 6) : ?>
                        <h1 class="text-[#21272A] text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                           What is your desired style of writing?
                        </h1>
                        
                        <!-- <p class="text-gray-600 text-lg mb-8">
                            Select 
                        </p> -->

                        <form class="flex flex-col gap-8">
                           <!-- Business Name Input -->
                            <div class="flex flex-col gap-2">
                                <!-- <label for="products_used" class="text-primary-main text-base font-medium">
                                  What is your desired style of writing?
                                </label> -->
                                 <!--  products_used -->
                                <div class="relative">
                                    <textarea 
                                    id="style_of_writing" 
                                    name="style_of_writing" 
                                    placeholder="Please enter what is your desired style of writing"
                                    rows="4"
                                    class="w-full !px-[16px] !py-[12px] rounded-lg !bg-ipt-bg-1
                                            focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                            placeholder:text-gray-400
                                            transition-all duration-200 resize-vertical"></textarea>
                                </div>
                            </div>
                           

                            <!-- Navigation Buttons -->
                            <div class="flex gap-4 mt-8">
                               <!-- Back Button -->
                               <button type="button" 
                                        onclick="goToStep(5)"
                                        class="flex-1 py-3 px-6 rounded-lg border border-btn-disable
                                            text-primary-main font-semibold hover:border-btn-disable
                                            hover:bg-gray-50 transition-all duration-200">
                                    Back
                                </button>

                                <!-- Continue Button -->
                                <button onclick="goToStep(7)" type="button" 
                                        id="continueBtn"
                                        class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                             transition-all duration-200"
                                        >
                                    Continue
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
          </div>
        </div>
        
        <!-- Right Content -->
        <div class="w-5/12 max-md:w-full min-h-screen">
          <div class="bg-brand-main-2 h-full flex grow flex-col items-stretch text-base text-[#BBB] font-semibold whitespace-nowrap tracking-tight w-full pt-[210px] pb-[60px] px-[74px] max-md:max-w-full max-md:mt-10 max-md:pt-[100px] max-md:px-5">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/ffcb3c9a2de94502ae520638565230e0/64a136b25eb3784de841fce1b8c0ffa54f7ec7d1?placeholderIfAbsent=true"
              class="aspect-[1.7] object-contain w-full max-md:max-w-full"
              alt="Onboarding illustration"
            />
            <div class="w-full flex text-right min-h-6 w-[34px] flex-col items-stretch justify-center mt-[119px] max-md:mr-[7px] max-md:mt-10">
                <a href="#" onclick="skipToEnd()" class="text-[#BBB] hover:text-white transition-colors duration-200">
                    Skip
                </a>
            </div>
          </div>
        </div>
      </div>
    </div>
</main><!-- #page -->
<style>
   input[type="radio"]:checked {
        border-color: #48C9B0 !important;
        background-color: #48C9B0 !important;
        box-shadow: none;
    }
    
</style>
<script>
    // Data collection and persistence system
    const GET_STARTED_DATA_KEY = 'get_started_form_data';
    const CUSTOMER_ID = <?php echo json_encode($customer_id); ?>;

    // Initialize or load existing data
    let formData = JSON.parse(localStorage.getItem(GET_STARTED_DATA_KEY)) || {
        industry: '',
        company_description: '',
        services_products: '',
        customer_demographics: '',
        products_used: '',
        style_of_writing: ''
    };

    // Save current step data to localStorage
    function saveCurrentStepData() {
        const currentStep = <?php echo $step; ?>;

        switch(currentStep) {
            case 2:
                const industrySelect = document.getElementById('industry');
                const companyDesc = document.getElementById('company_description');
                if (industrySelect) formData.industry = industrySelect.value || '';
                if (companyDesc) formData.company_description = companyDesc.value || '';
                break;
            case 3:
                const servicesProducts = document.getElementById('services_products');
                if (servicesProducts) formData.services_products = servicesProducts.value || '';
                break;
            case 4:
                const customerDemo = document.getElementById('customer_demographics');
                if (customerDemo) formData.customer_demographics = customerDemo.value || '';
                break;
            case 5:
                const productsUsed = document.getElementById('products_used');
                if (productsUsed) formData.products_used = productsUsed.value || '';
                break;
            case 6:
                const styleWriting = document.getElementById('style_of_writing');
                if (styleWriting) formData.style_of_writing = styleWriting.value || '';
                break;
        }

        localStorage.setItem(GET_STARTED_DATA_KEY, JSON.stringify(formData));
    }

    // Load saved data into current step fields
    function loadSavedData() {
        const currentStep = <?php echo $step; ?>;

        switch(currentStep) {
            case 2:
                const industrySelect = document.getElementById('industry');
                const companyDesc = document.getElementById('company_description');

                // Load company description immediately
                if (companyDesc && formData.company_description) {
                    companyDesc.value = formData.company_description;
                }

                // Load industry selection after dropdown is populated
                if (industrySelect && formData.industry) {
                    setTimeout(() => {
                        // Find the option with the saved value and select it
                        const options = industrySelect.querySelectorAll('option');
                        for (let option of options) {
                            if (option.value === formData.industry) {
                                option.selected = true;
                                industrySelect.value = formData.industry;

                                // Also update the dropdown visual state
                                const dropdownItems = document.querySelectorAll('#industryDropdown [data-industry-id]');
                                dropdownItems.forEach(item => {
                                    item.classList.remove('bg-ipt-bg-1');
                                    if (item.getAttribute('data-industry-id') === formData.industry) {
                                        item.classList.add('bg-ipt-bg-1');
                                    }
                                });
                                break;
                            }
                        }
                    }, 1000); // Wait for industry options to load
                }
                break;
            case 3:
                const servicesProducts = document.getElementById('services_products');
                if (servicesProducts && formData.services_products) {
                    servicesProducts.value = formData.services_products;
                }
                break;
            case 4:
                const customerDemo = document.getElementById('customer_demographics');
                if (customerDemo && formData.customer_demographics) {
                    customerDemo.value = formData.customer_demographics;
                }
                break;
            case 5:
                const productsUsed = document.getElementById('products_used');
                if (productsUsed && formData.products_used) {
                    productsUsed.value = formData.products_used;
                }
                break;
            case 6:
                const styleWriting = document.getElementById('style_of_writing');
                if (styleWriting && formData.style_of_writing) {
                    styleWriting.value = formData.style_of_writing;
                }
                break;
        }
    }

    // Save data to GraphQL API
    async function saveDataToAPI() {
        try {
            // Save current step data before API call
            saveCurrentStepData();

            const mutation = `
                mutation Webhooks_users_update_info($id: Int!, $info: JSON) {
                    webhooks_users_update_info(id: $id, input: { info: $info }) {
                        id
                        info
                    }
                }
            `;

            const variables = {
                id: parseInt(CUSTOMER_ID),
                info: formData
            };

            const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'ipt_home_graphql',
                    query: mutation,
                    variables: JSON.stringify(variables)
                })
            });

            const result = await response.json();

            if (result.errors && result.errors.length > 0) {
                console.error('GraphQL Error:', result.errors[0].message);
                throw new Error(result.errors[0].message);
            }

            if (result.data && result.data.webhooks_users_update_info) {
                console.log('Data saved successfully:', result.data.webhooks_users_update_info);
                // Clear localStorage after successful save
                localStorage.removeItem(GET_STARTED_DATA_KEY);
                return true;
            } else {
                throw new Error('Invalid response from server');
            }

        } catch (error) {
            console.error('Failed to save data:', error);
            // Don't clear localStorage on error, so user can retry
            return false;
        }
    }

    // Skip to end function
    async function skipToEnd() {
        const success = await saveDataToAPI();
        // Redirect regardless of API success/failure
        window.location.href = '<?php echo site_url('customer/create_website'); ?>';
    }

    // Modified goToStep function
    function goToStep(step) {
        // Save current step data before navigation
        saveCurrentStepData();

        if(step == 7) {
            // Save data and redirect to customer dashboard
            saveDataToAPI().then(() => {
                window.location.href = '<?php echo site_url('customer/create_website'); ?>';
            });
        } else {
            // Get current URL
            const currentUrl = new URL(window.location.href);

            // Update or add new step parameter
            currentUrl.searchParams.set('step', step);

            // Redirect to new URL
            window.location.href = currentUrl.toString();
        }
    }

    // Load saved data when page loads
    document.addEventListener('DOMContentLoaded', function() {
        loadSavedData();

        // Add event listeners to save data on input changes
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', saveCurrentStepData);
            input.addEventListener('input', saveCurrentStepData);
        });
    });
</script>
<?php
get_footer(null, array(
    'hide_footer' => true,
)); 
