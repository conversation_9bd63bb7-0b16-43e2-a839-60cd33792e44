<?php
/**
 * Template Name: Get Started page template
 *
 * @package ipt_home
 */

get_header(null, array(
    'hide_menu' => true,
));

$step = $_GET['step'] ?? 1;
$total_steps = 6;
?>
<main class="min-h-screen w-full">
    <div class="bg-white overflow-hidden">
      <div class="gap-5 flex max-md:flex-col max-md:items-stretch">
        <!-- Left Content -->
        <div class="w-7/12 max-md:w-full min-h-full">
          <div class="flex w-full flex-col max-md:max-w-full">
            <!-- Logo -->
            <div class="flex items-center pl-[16px] md:pl-[80px] gap-1 overflow-hidden text-base text-brand-main font-bold whitespace-nowrap tracking-tight py-[7px]">
              <img
                src="https://cdn.builder.io/api/v1/image/assets/ffcb3c9a2de94502ae520638565230e0/5e2c751f3df0530176bcf2a042269af9fcc94c33?placeholderIfAbsent=true"
                class="aspect-[1] object-contain w-8 self-stretch shrink-0 my-auto"
                alt="Weaveform Logo"
              />
              <div class="bg-blend-normal self-stretch my-auto">Weaveform</div>
            </div>
            
            <div class="flex max-w-full  flex-col items-stretch mt-[29px] px-[16px] md:pl-[188px] md:pr-[128px]">
              <!-- Progress Indicator -->
                <div class="flex items-center gap-3 min-h-[40px] mb-4">
                    <?php for($i = 2; $i <= $total_steps; $i++) : ?>
                    <div class="self-stretch flex w-[60px] shrink-0 h-[5px] my-auto rounded-[80px] <?php 
            echo ($i <= $step) ? 'bg-brand-main' : 'bg-disable-step'; 
        ?>" aria-label="Step <?php echo $i; ?>"></div>
                    <?php endfor; ?>
                    <!-- <div class="self-stretch flex w-[60px] shrink-0 h-[5px] my-auto rounded-[80px] bg-disable-step" aria-label="Step 2"></div>
                    <div class="self-stretch flex w-[60px] shrink-0 h-[5px] my-auto rounded-[80px] bg-disable-step" aria-label="Step 3"></div>
                    <div class="self-stretch flex w-[60px] shrink-0 h-[5px] my-auto rounded-[80px] bg-disable-step" aria-label="Step 4"></div> -->
                </div>
                <!-- Content Step -->
                <div class="flex flex-col justify-start min-h-[300px] h-auto text-base gap-[16px] font-semibold tracking-tight max-md:max-w-full">
                   
                    <!-- Step 1 -->
                    <?php if ($step == 1) : ?>
                        <h1 class="text-primary-main text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                            Welcome to Weaveform!
                        </h1>
                        <p class="text-black font-medium leading-6 mt-4 max-md:max-w-full">
                            We'll guide you through a few simple steps to set up your fully
                            managed website – no technical know-how required.
                        </p>
                        <div class="mt-4 w-[196px] max-w-full">
                            <!-- Get started button -->
                            <button 
                                onclick="goToStep(2)"
                                class="mx-auto rounded-[8px] text-primary-main bg-brand-main hover:bg-brand-main/80 text-16 font-semibold self-stretch cursor-pointer  py-[12px] px-[20px] border-[none] w-[196px]">
                                Get Started
                            </button>
                        </div>
                    <?php endif; ?>
                     <!-- Step 2 New -->
                    <?php if ($step == 2) : ?>
                        <h1 class="text-[#21272A] text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                            What is your industry?
                        </h1>
                        
                        <p class="text-gray-600 text-lg mb-8">
                            Select the industry that best represent your brand.
                        </p>

                        <form class="flex flex-col gap-8">
                            <!-- Industry Dropdown -->
                            <div class="flex flex-col gap-2">
                                <label class="text-[#21272A] text-xl font-medium">
                                    Industry Options
                                </label>
                                <!-- Industry Dropdown Container -->
                                <div class="relative z-30">
                                    <select id="industry" 
                                            class="w-full !px-[16px] !py-[12px] rounded-lg border border-[#E6E9F5] 
                                                appearance-none bg-white
                                                focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                                cursor-pointer
                                                transition-all duration-200 !h-[48px] text-16 !bg-ipt-bg-1 select-custom">
                                        <option value="" disabled selected>Select Industry</option>
                                    </select>
                                    <!-- Arrow Icon -->
                                    <!-- <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </div> -->
                                    <!-- Custom Dropdown Content -->
                                    <div id="industryDropdown" 
                                        class="dropdown-content absolute left-0 right-0 top-full mt-1 hidden
                                                !bg-white rounded-lg border border-[#E6E9F5] shadow-lg
                                                max-h-[300px] overflow-y-auto
                                                overflow-hidden z-100">
                                        <!-- Dropdown Items -->
                                        <div class="py-2 bg-white">
                                           
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--  Company Description -->
                            <div class="flex flex-col gap-2">
                                <label class="text-[#21272A] text-xl font-medium">
                                    Company Description
                                </label>
                                <!--  Company Description -->
                                <div class="relative">
                                    <textarea 
                                    id="company_description" 
                                    name="company_description" 
                                    placeholder="Please enter your company description"
                                    rows="4"
                                    class="w-full !px-[16px] !py-[12px] rounded-lg !bg-ipt-bg-1
                                            focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                            placeholder:text-gray-400
                                            transition-all duration-200 resize-vertical"></textarea>
                                </div>
                            </div>
                            <script>
                                jQuery(document).ready(function($) {
                                    /* Industry list */
                                    const queryIndustry = `
                                        query Industry_list {
                                            industry_list(body: { filters: null }) {
                                                id
                                                name
                                            }
                                        }
                                    `;

                                    $.ajax({
                                        url: iptHomeAjax.ajax_url,
                                        type: 'POST',
                                        dataType: 'json',
                                        data: {
                                            action: 'ipt_home_graphql',
                                            query: queryIndustry,
                                            variables: {}
                                        },
                                        success: function(response) {
                                            if(response.data && response.data.industry_list) {      
                                                renderIndustries(response.data.industry_list);
                                            } else {
                                                // No data
                                            }
                                        },
                                        error: function(xhr, status, error) {
                                        }
                                    });

                                    // Hàm render ra HTML
                                    function renderIndustries(list) {
                                        let html = '';  
                                        list.forEach(function(item) {
                                            html += `
                                                 <option value="${item.id}">${item.name}</option>
                                            `;      
                                        });
                                        $('#industry').html(html);

                                        let html_dropdown = '';  
                                        list.forEach(function(item) {
                                            html_dropdown += `
                                                <div data-industry-id="${item.id}" class="px-4 py-2 hover:bg-ipt-bg-1 cursor-pointer transition-colors duration-150">
                                                    ${item.name}
                                                </div>
                                            `;      
                                        });
                                        $('#industryDropdown').html(html_dropdown);
                                    }   
                                    
                                });
                            </script> 

                            <!-- Navigation Buttons -->
                            <div class="flex gap-4 mt-8">
                               <!-- Back Button -->
                               <button type="button" 
                                        onclick="goToStep(2)"
                                        class="flex-1 py-3 px-6 rounded-lg border border-btn-disable
                                            text-primary-main font-semibold hover:border-btn-disable
                                            hover:bg-gray-50 transition-all duration-200">
                                    Back
                                </button>

                                <!-- Continue Button -->
                                <button onclick="goToStep(3)" type="button" 
                                        id="continueBtn"
                                        class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                             transition-all duration-200"
                                        >
                                    Continue
                                </button>
                            </div>
                        </form>
                        <script>
                           document.addEventListener('DOMContentLoaded', function() {
                                // Function to initialize dropdown
                                function initializeDropdown(selectId, dropdownId) {
                                    const select = document.getElementById(selectId);
                                    const dropdown = document.getElementById(dropdownId);
                                    const dropdownItems = dropdown.querySelectorAll('.cursor-pointer');
                                    const selectContainer = select.parentElement;

                                    // Click on container to toggle dropdown
                                    selectContainer.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        
                                        // Close all other dropdowns
                                        document.querySelectorAll('.dropdown-content').forEach(d => {
                                            if (d.id !== dropdownId) {
                                                d.classList.add('hidden');
                                            }
                                        });
                                        
                                        dropdown.classList.toggle('hidden');
                                    });

                                    // Handle item selection
                                    dropdownItems.forEach(item => {
                                        item.addEventListener('click', function(e) {
                                            e.stopPropagation();
                                            const selectedValue = this.textContent.trim();
                                            
                                            // Update value for select element
                                            const option = document.createElement('option');
                                            option.value = selectedValue.toLowerCase();
                                            option.text = selectedValue;
                                            option.selected = true;
                                            
                                            // Remove old option and add new option
                                            select.innerHTML = '';
                                            select.appendChild(option);
                                            
                                            // Update displayed text on select
                                            select.value = selectedValue.toLowerCase();
                                            
                                            // Hide dropdown
                                            dropdown.classList.add('hidden');
                                            
                                            // Trigger change event
                                            select.dispatchEvent(new Event('change'));
                                            
                                            // Update selected state in dropdown
                                            dropdownItems.forEach(di => di.classList.remove('bg-ipt-bg-1'));
                                            this.classList.add('bg-ipt-bg-1');
                                        });
                                    });

                                    // Close dropdown when clicking outside
                                    document.addEventListener('click', function(e) {
                                        if (!selectContainer.contains(e.target)) {
                                            dropdown.classList.add('hidden');
                                        }
                                    });
                                }

                                // Initialize all necessary dropdowns
                                const dropdownConfigs = [
                                    { selectId: 'industry', dropdownId: 'industryDropdown' },
                                    { selectId: 'style', dropdownId: 'styleDropdown' }
                                ];

                                // Initialize all dropdowns
                                dropdownConfigs.forEach(config => {
                                    initializeDropdown(config.selectId, config.dropdownId);
                                });

                                // Add validation for form
                                function validateDropdowns() {
                                    const isValid = dropdownConfigs.every(config => 
                                        document.getElementById(config.selectId).value !== ''
                                    );

                                    const continueBtn = document.getElementById('continueBtn');
                                    if (continueBtn) {
                                        if (isValid) {
                                            continueBtn.disabled = false;
                                            continueBtn.classList.remove('bg-disable-step', 'cursor-not-allowed', 'text-white');
                                            continueBtn.classList.add('bg-brand-main', 'cursor-pointer', 'text-primary-main');
                                        } else {
                                            continueBtn.disabled = true;
                                            continueBtn.classList.add('bg-disable-step', 'cursor-not-allowed', 'text-white');
                                            continueBtn.classList.remove('bg-brand-main', 'cursor-pointer', 'text-primary-main');
                                        }
                                    }
                                }

                                // Add change listeners for validation
                                dropdownConfigs.forEach(config => {
                                    document.getElementById(config.selectId).addEventListener('change', validateDropdowns);
                                });
                            });
                        </script>
                        <style>
                            .select-custom {
                                -webkit-appearance: none !important;
                                -moz-appearance: none !important;
                                appearance: none !important;
                                pointer-events: none;
                            }
                            .select-custom::-ms-expand {
                                display: none;
                            }
                            .select-custom option {
                                display: none;
                            }
                            
                            /* Custom scrollbar for dropdown */
                            .dropdown-content {
                                background-color: #fff;
                                z-index: 9999 !important;
                            }
                            .dropdown-content::-webkit-scrollbar {
                                width: 8px;
                            }

                            .dropdown-content::-webkit-scrollbar-track {
                                background: #f1f1f1;
                                border-radius: 4px;
                            }

                            .dropdown-content::-webkit-scrollbar-thumb {
                                background: #888;
                                border-radius: 4px;
                            }

                            .dropdown-content::-webkit-scrollbar-thumb:hover {
                                background: #555;
                            }

                            /* Ensure dropdown is above other elements */
                            .relative {
                                position: relative;
                            }
                        </style>
                    <?php endif; ?>

                    <!-- Step 3 -->
                    <?php if ($step == 3) : ?>
                        <h1 class="text-primary-main text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                           Which services or products do you offer?
                        </h1>

                        <form class="flex flex-col gap-8 mt-8">
                            <!-- Business Name Input -->
                            <div class="flex flex-col gap-2">
                                <label for="services_products" class="text-primary-main text-base font-medium">
                                    Please enter your services or products
                                </label>
                                
                                <!--  services_products -->
                                <div class="relative">
                                    <textarea 
                                    id="services_products" 
                                    name="services_products" 
                                    placeholder="Please enter your company description"
                                    rows="4"
                                    class="w-full !px-[16px] !py-[12px] rounded-lg !bg-ipt-bg-1
                                            focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                            placeholder:text-gray-400
                                            transition-all duration-200 resize-vertical"></textarea>
                                </div>
                            </div>

                         

                            <!-- Navigation Buttons -->
                            <div class="flex gap-4 mt-8">
                                <!-- Back Button -->
                                <button type="button" 
                                        onclick="goToStep(2)"
                                        class="flex-1 py-3 px-6 rounded-lg border border-btn-disable
                                            text-primary-main font-semibold hover:border-btn-disable
                                            hover:bg-gray-50 transition-all duration-200">
                                    Back
                                </button>

                                <!-- Continue Button -->
                                <button type="button" 
                                        id="continueBtn"
                                        onclick="goToStep(4)"
                                        class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                             transition-all duration-200"
                                        >
                                    Continue
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>

                    <!-- Step 4 -->
                    <?php if ($step == 4) : ?>
                        <h1 class="text-primary-main text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                          What are your customer demographics (e.g., age, gender, interests, ethnicity, occupation)?
                        </h1>

                        <form class="flex flex-col gap-8 mt-8">
                            <!-- Business Name Input -->
                            <div class="flex flex-col gap-2">
                                <label for="customer_demographics" class="text-primary-main text-base font-medium">
                                    Please enter your customer demographics
                                </label>
                                 <!--  customer_demographics -->
                                <div class="relative">
                                    <textarea 
                                    id="customer_demographics" 
                                    name="customer_demographics" 
                                    placeholder="Please enter your customer demographics"
                                    rows="4"
                                    class="w-full !px-[16px] !py-[12px] rounded-lg !bg-ipt-bg-1
                                            focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                            placeholder:text-gray-400
                                            transition-all duration-200 resize-vertical"></textarea>
                                </div>
                            </div>

                         

                            <!-- Navigation Buttons -->
                            <div class="flex gap-4 mt-8">
                                <!-- Back Button -->
                                <button type="button" 
                                        onclick="goToStep(2)"
                                        class="flex-1 py-3 px-6 rounded-lg border border-btn-disable
                                            text-primary-main font-semibold hover:border-btn-disable
                                            hover:bg-gray-50 transition-all duration-200">
                                    Back
                                </button>

                                <!-- Continue Button -->
                                <button type="button" 
                                        id="continueBtn"
                                        onclick="goToStep(5)"
                                        class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                             transition-all duration-200"
                                        >
                                    Continue
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>

                    <!-- Step 5 New -->
                    <?php if ($step == 5) : ?>
                        <h1 class="text-[#21272A] text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                            Where are your products used?
                        </h1>
                       
                        <form class="flex flex-col gap-8">
                            <!-- Business Name Input -->
                            <div class="flex flex-col gap-2">
                                <label for="products_used" class="text-primary-main text-base font-medium">
                                   Where are your products used?
                                </label>
                                 <!--  products_used -->
                                <div class="relative">
                                    <textarea 
                                    id="products_used" 
                                    name="products_used" 
                                    placeholder="Please enter where are your products used"
                                    rows="4"
                                    class="w-full !px-[16px] !py-[12px] rounded-lg !bg-ipt-bg-1
                                            focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                            placeholder:text-gray-400
                                            transition-all duration-200 resize-vertical"></textarea>
                                </div>
                            </div>
                        
                            <!-- Navigation Buttons -->
                            <div class="flex gap-4 mt-8">
                               <!-- Back Button -->
                               <button type="button" 
                                        onclick="goToStep(3)"
                                        class="flex-1 py-3 px-6 rounded-lg border border-btn-disable
                                            text-primary-main font-semibold hover:border-btn-disable
                                            hover:bg-gray-50 transition-all duration-200">
                                    Back
                                </button>

                                <!-- Continue Button -->
                                <button onclick="goToStep(6)" type="button" 
                                        id="continueBtn"
                                        class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                             transition-all duration-200"
                                        >
                                    Continue
                                </button>
                            </div>
                        </form>
                        <script>
                           document.addEventListener('DOMContentLoaded', function() {
                                // Function to initialize dropdown
                                function initializeDropdown(selectId, dropdownId) {
                                    const select = document.getElementById(selectId);
                                    const dropdown = document.getElementById(dropdownId);
                                    const dropdownItems = dropdown.querySelectorAll('.cursor-pointer');
                                    const selectContainer = select.parentElement;

                                    // Click on container to toggle dropdown
                                    selectContainer.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        
                                        // Close all other dropdowns
                                        document.querySelectorAll('.dropdown-content').forEach(d => {
                                            if (d.id !== dropdownId) {
                                                d.classList.add('hidden');
                                            }
                                        });
                                        
                                        dropdown.classList.toggle('hidden');
                                    });

                                    // Handle item selection
                                    dropdownItems.forEach(item => {
                                        item.addEventListener('click', function(e) {
                                            e.stopPropagation();
                                            const selectedValue = this.textContent.trim();
                                            
                                            // Update value for select element
                                            const option = document.createElement('option');
                                            option.value = selectedValue.toLowerCase();
                                            option.text = selectedValue;
                                            option.selected = true;
                                            
                                            // Remove old option and add new option
                                            select.innerHTML = '';
                                            select.appendChild(option);
                                            
                                            // Update displayed text on select
                                            select.value = selectedValue.toLowerCase();
                                            
                                            // Hide dropdown
                                            dropdown.classList.add('hidden');
                                            
                                            // Trigger change event
                                            select.dispatchEvent(new Event('change'));
                                            
                                            // Update selected state in dropdown
                                            dropdownItems.forEach(di => di.classList.remove('bg-ipt-bg-1'));
                                            this.classList.add('bg-ipt-bg-1');
                                        });
                                    });

                                    // Close dropdown when clicking outside
                                    document.addEventListener('click', function(e) {
                                        if (!selectContainer.contains(e.target)) {
                                            dropdown.classList.add('hidden');
                                        }
                                    });
                                }

                                // Initialize all necessary dropdowns
                                const dropdownConfigs = [
                                    { selectId: 'industry', dropdownId: 'industryDropdown' },
                                    { selectId: 'style', dropdownId: 'styleDropdown' }
                                ];

                                // Initialize all dropdowns
                                dropdownConfigs.forEach(config => {
                                    initializeDropdown(config.selectId, config.dropdownId);
                                });

                                // Add validation for form
                                function validateDropdowns() {
                                    const isValid = dropdownConfigs.every(config => 
                                        document.getElementById(config.selectId).value !== ''
                                    );

                                    const continueBtn = document.getElementById('continueBtn');
                                    if (continueBtn) {
                                        if (isValid) {
                                            continueBtn.disabled = false;
                                            continueBtn.classList.remove('bg-disable-step', 'cursor-not-allowed', 'text-white');
                                            continueBtn.classList.add('bg-brand-main', 'cursor-pointer', 'text-primary-main');
                                        } else {
                                            continueBtn.disabled = true;
                                            continueBtn.classList.add('bg-disable-step', 'cursor-not-allowed', 'text-white');
                                            continueBtn.classList.remove('bg-brand-main', 'cursor-pointer', 'text-primary-main');
                                        }
                                    }
                                }

                                // Add change listeners for validation
                                dropdownConfigs.forEach(config => {
                                    document.getElementById(config.selectId).addEventListener('change', validateDropdowns);
                                });
                            });
                        </script>
                        <style>
                            .select-custom {
                                -webkit-appearance: none !important;
                                -moz-appearance: none !important;
                                appearance: none !important;
                                pointer-events: none;
                            }
                            .select-custom::-ms-expand {
                                display: none;
                            }
                            .select-custom option {
                                display: none;
                            }
                            
                            /* Custom scrollbar for dropdown */
                            .dropdown-content {
                                background-color: #fff;
                                z-index: 9999 !important;
                            }
                            .dropdown-content::-webkit-scrollbar {
                                width: 8px;
                            }

                            .dropdown-content::-webkit-scrollbar-track {
                                background: #f1f1f1;
                                border-radius: 4px;
                            }

                            .dropdown-content::-webkit-scrollbar-thumb {
                                background: #888;
                                border-radius: 4px;
                            }

                            .dropdown-content::-webkit-scrollbar-thumb:hover {
                                background: #555;
                            }

                            /* Ensure dropdown is above other elements */
                            .relative {
                                position: relative;
                            }
                        </style>
                    <?php endif; ?>

                    <!-- Step 6 New -->
                    <?php if ($step == 6) : ?>
                        <h1 class="text-[#21272A] text-32 md:text-44 leading-none tracking-tighter max-md:max-w-full">
                           What is your desired style of writing?
                        </h1>
                        
                        <!-- <p class="text-gray-600 text-lg mb-8">
                            Select 
                        </p> -->

                        <form class="flex flex-col gap-8">
                           <!-- Business Name Input -->
                            <div class="flex flex-col gap-2">
                                <!-- <label for="products_used" class="text-primary-main text-base font-medium">
                                  What is your desired style of writing?
                                </label> -->
                                 <!--  products_used -->
                                <div class="relative">
                                    <textarea 
                                    id="style_of_writing" 
                                    name="style_of_writing" 
                                    placeholder="Please enter what is your desired style of writing"
                                    rows="4"
                                    class="w-full !px-[16px] !py-[12px] rounded-lg !bg-ipt-bg-1
                                            focus:outline-none focus:border-brand-main focus:ring-1 focus:ring-brand-main
                                            placeholder:text-gray-400
                                            transition-all duration-200 resize-vertical"></textarea>
                                </div>
                            </div>
                           

                            <!-- Navigation Buttons -->
                            <div class="flex gap-4 mt-8">
                               <!-- Back Button -->
                               <button type="button" 
                                        onclick="goToStep(5)"
                                        class="flex-1 py-3 px-6 rounded-lg border border-btn-disable
                                            text-primary-main font-semibold hover:border-btn-disable
                                            hover:bg-gray-50 transition-all duration-200">
                                    Back
                                </button>

                                <!-- Continue Button -->
                                <button onclick="goToStep(7)" type="button" 
                                        id="continueBtn"
                                        class="flex-1 py-3 px-6 rounded-lg bg-brand-main hover:bg-brand-main/80 text-primary-main font-semibold
                                             transition-all duration-200"
                                        >
                                    Continue
                                </button>
                            </div>
                        </form>
                        <script>
                           document.addEventListener('DOMContentLoaded', function() {
                                // Function to initialize dropdown
                                function initializeDropdown(selectId, dropdownId) {
                                    const select = document.getElementById(selectId);
                                    const dropdown = document.getElementById(dropdownId);
                                    const dropdownItems = dropdown.querySelectorAll('.cursor-pointer');
                                    const selectContainer = select.parentElement;

                                    // Click on container to toggle dropdown
                                    selectContainer.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        
                                        // Close all other dropdowns
                                        document.querySelectorAll('.dropdown-content').forEach(d => {
                                            if (d.id !== dropdownId) {
                                                d.classList.add('hidden');
                                            }
                                        });
                                        
                                        dropdown.classList.toggle('hidden');
                                    });

                                    // Handle item selection
                                    dropdownItems.forEach(item => {
                                        item.addEventListener('click', function(e) {
                                            e.stopPropagation();
                                            const selectedValue = this.textContent.trim();
                                            
                                            // Update value for select element
                                            const option = document.createElement('option');
                                            option.value = selectedValue.toLowerCase();
                                            option.text = selectedValue;
                                            option.selected = true;
                                            
                                            // Remove old option and add new option
                                            select.innerHTML = '';
                                            select.appendChild(option);
                                            
                                            // Update displayed text on select
                                            select.value = selectedValue.toLowerCase();
                                            
                                            // Hide dropdown
                                            dropdown.classList.add('hidden');
                                            
                                            // Trigger change event
                                            select.dispatchEvent(new Event('change'));
                                            
                                            // Update selected state in dropdown
                                            dropdownItems.forEach(di => di.classList.remove('bg-ipt-bg-1'));
                                            this.classList.add('bg-ipt-bg-1');
                                        });
                                    });

                                    // Close dropdown when clicking outside
                                    document.addEventListener('click', function(e) {
                                        if (!selectContainer.contains(e.target)) {
                                            dropdown.classList.add('hidden');
                                        }
                                    });
                                }

                                // Initialize all necessary dropdowns
                                const dropdownConfigs = [
                                    { selectId: 'industry', dropdownId: 'industryDropdown' },
                                    { selectId: 'style', dropdownId: 'styleDropdown' }
                                ];

                                // Initialize all dropdowns
                                dropdownConfigs.forEach(config => {
                                    initializeDropdown(config.selectId, config.dropdownId);
                                });

                                // Add validation for form
                                function validateDropdowns() {
                                    const isValid = dropdownConfigs.every(config => 
                                        document.getElementById(config.selectId).value !== ''
                                    );

                                    const continueBtn = document.getElementById('continueBtn');
                                    if (continueBtn) {
                                        if (isValid) {
                                            continueBtn.disabled = false;
                                            continueBtn.classList.remove('bg-disable-step', 'cursor-not-allowed', 'text-white');
                                            continueBtn.classList.add('bg-brand-main', 'cursor-pointer', 'text-primary-main');
                                        } else {
                                            continueBtn.disabled = true;
                                            continueBtn.classList.add('bg-disable-step', 'cursor-not-allowed', 'text-white');
                                            continueBtn.classList.remove('bg-brand-main', 'cursor-pointer', 'text-primary-main');
                                        }
                                    }
                                }

                                // Add change listeners for validation
                                dropdownConfigs.forEach(config => {
                                    document.getElementById(config.selectId).addEventListener('change', validateDropdowns);
                                });
                            });
                        </script>
                        <style>
                            .select-custom {
                                -webkit-appearance: none !important;
                                -moz-appearance: none !important;
                                appearance: none !important;
                                pointer-events: none;
                            }
                            .select-custom::-ms-expand {
                                display: none;
                            }
                            .select-custom option {
                                display: none;
                            }
                            
                            /* Custom scrollbar for dropdown */
                            .dropdown-content {
                                background-color: #fff;
                                z-index: 9999 !important;
                            }
                            .dropdown-content::-webkit-scrollbar {
                                width: 8px;
                            }

                            .dropdown-content::-webkit-scrollbar-track {
                                background: #f1f1f1;
                                border-radius: 4px;
                            }

                            .dropdown-content::-webkit-scrollbar-thumb {
                                background: #888;
                                border-radius: 4px;
                            }

                            .dropdown-content::-webkit-scrollbar-thumb:hover {
                                background: #555;
                            }

                            /* Ensure dropdown is above other elements */
                            .relative {
                                position: relative;
                            }
                        </style>
                    <?php endif; ?>
                </div>
            </div>
          </div>
        </div>
        
        <!-- Right Content -->
        <div class="w-5/12 max-md:w-full min-h-screen">
          <div class="bg-brand-main-2 h-full flex grow flex-col items-stretch text-base text-[#BBB] font-semibold whitespace-nowrap tracking-tight w-full pt-[210px] pb-[60px] px-[74px] max-md:max-w-full max-md:mt-10 max-md:pt-[100px] max-md:px-5">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/ffcb3c9a2de94502ae520638565230e0/64a136b25eb3784de841fce1b8c0ffa54f7ec7d1?placeholderIfAbsent=true"
              class="aspect-[1.7] object-contain w-full max-md:max-w-full"
              alt="Onboarding illustration"
            />
            <div class="w-full flex text-right min-h-6 w-[34px] flex-col items-stretch justify-center mt-[119px] max-md:mr-[7px] max-md:mt-10">
                Skip
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
</main><!-- #page -->
<style>
   input[type="radio"]:checked {
        border-color: #48C9B0 !important;
        background-color: #48C9B0 !important;
        box-shadow: none;
    }
    
</style>
<script>
    function goToStep(step) {
        if(step == 7) {
            // Redirect to customer dashboard
            window.location.href = '<?php echo site_url('customer/create_website'); ?>';
           
        } else {
            // Get current URL
            const currentUrl = new URL(window.location.href);
            
            // Update or add new step parameter
            currentUrl.searchParams.set('step', step);
            
            // Redirect to new URL
            window.location.href = currentUrl.toString();
        }
    }
</script>
<?php
get_footer(null, array(
    'hide_footer' => true,
)); 
